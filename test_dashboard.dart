import 'package:flutter/material.dart';
import 'package:tajer_plus/core/theme/index.dart';
import 'package:tajer_plus/core/widgets/enhanced_ui_components.dart';

void main() {
  runApp(const TestDashboardApp());
}

class TestDashboardApp extends StatelessWidget {
  const TestDashboardApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تاجر بلس - اختبار الداش بورد',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: ThemeMode.system,
      home: const TestDashboardScreen(),
    );
  }
}

class TestDashboardScreen extends StatefulWidget {
  const TestDashboardScreen({super.key});

  @override
  State<TestDashboardScreen> createState() => _TestDashboardScreenState();
}

class _TestDashboardScreenState extends State<TestDashboardScreen> {
  String selectedTheme = 'red';
  bool isDarkMode = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('🎨 تاجر بلس - الداش بورد الجديد'),
        centerTitle: true,
        actions: [
          // زر تغيير الثيم
          PopupMenuButton<String>(
            icon: const Icon(Icons.palette_outlined),
            onSelected: (value) {
              setState(() {
                selectedTheme = value;
              });
              // تطبيق الثيم الجديد
              _applyTheme(value);
            },
            itemBuilder: (context) =>
                AppColors.availableThemes.entries.map((entry) {
              return PopupMenuItem<String>(
                value: entry.key,
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: entry.value['primary'] as Color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(entry.value['name'] as String),
                  ],
                ),
              );
            }).toList(),
          ),
          // زر تغيير الوضع
          IconButton(
            icon: Icon(isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              setState(() {
                isDarkMode = !isDarkMode;
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الترحيب
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.dashboard_rounded,
                    color: Colors.white,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مرحباً بك في تاجر بلس الجديد! 🚀',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'تصميم عصري مع Material Design 3',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // بطاقات الإحصائيات المتحركة
            Text(
              '📊 بطاقات الإحصائيات المتحركة',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: EnhancedUIComponents.animatedStatsCard(
                    title: 'المبيعات',
                    value: '125,480 ر.س',
                    subtitle: 'إجمالي المبيعات',
                    icon: Icons.shopping_cart_outlined,
                    context: context,
                    customColor: AppColors.moduleSales,
                    showTrend: true,
                    trendValue: 12.5,
                    isPositiveTrend: true,
                    showSparkline: true,
                    sparklineData: [15000, 18000, 16500, 22000, 25480],
                    animationDuration: const Duration(milliseconds: 1000),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: EnhancedUIComponents.animatedStatsCard(
                    title: 'المشتريات',
                    value: '89,320 ر.س',
                    subtitle: 'إجمالي المشتريات',
                    icon: Icons.shopping_bag_outlined,
                    context: context,
                    customColor: AppColors.modulePurchases,
                    showTrend: true,
                    trendValue: -5.2,
                    isPositiveTrend: false,
                    showSparkline: true,
                    sparklineData: [12000, 14500, 11800, 13200, 10500],
                    animationDuration: const Duration(milliseconds: 1200),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: EnhancedUIComponents.interactiveStatsCard(
                    title: 'الأرباح',
                    value: '36,160 ر.س',
                    subtitle: 'صافي الأرباح',
                    icon: Icons.trending_up_outlined,
                    context: context,
                    customColor: AppColors.success,
                    showProgress: true,
                    progressValue: 0.75,
                    showComparison: true,
                    comparisonText: 'مقارنة بالشهر الماضي',
                    comparisonValue: 12.5,
                    isComparisonPositive: true,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: EnhancedUIComponents.interactiveStatsCard(
                    title: 'الفواتير',
                    value: '248',
                    subtitle: 'عدد الفواتير',
                    icon: Icons.receipt_long_outlined,
                    context: context,
                    customColor: AppColors.moduleAccounts,
                    showProgress: true,
                    progressValue: 0.60,
                    showComparison: true,
                    comparisonText: 'فواتير جديدة هذا الأسبوع',
                    comparisonValue: 8.3,
                    isComparisonPositive: true,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // بطاقات الوصول السريع
            Text(
              '⚡ الوصول السريع الحديث',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: EnhancedUIComponents.modernQuickAccessCard(
                    title: 'بيع جديد',
                    icon: Icons.shopping_cart_outlined,
                    onTap: () => _showSnackBar('تم النقر على بيع جديد'),
                    context: context,
                    customColor: AppColors.moduleSales,
                    isNew: true,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: EnhancedUIComponents.modernQuickAccessCard(
                    title: 'إضافة منتج',
                    icon: Icons.inventory_2_outlined,
                    onTap: () => _showSnackBar('تم النقر على إضافة منتج'),
                    context: context,
                    customColor: AppColors.moduleProducts,
                    badge: '5+',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: EnhancedUIComponents.modernQuickAccessCard(
                    title: 'إضافة عميل',
                    icon: Icons.person_add_outlined,
                    onTap: () => _showSnackBar('تم النقر على إضافة عميل'),
                    context: context,
                    customColor: AppColors.moduleUsers,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: EnhancedUIComponents.modernQuickAccessCard(
                    title: 'المخزون',
                    icon: Icons.warehouse_outlined,
                    onTap: () => _showSnackBar('تم النقر على المخزون'),
                    context: context,
                    customColor: AppColors.moduleInventory,
                    badge: '!',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // معلومات التحسينات
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        color: theme.colorScheme.primary,
                        size: 28,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'التحسينات الجديدة',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem('🎨', 'ألوان Material Design 3 العصرية'),
                  _buildFeatureItem('♿', 'ضمان الوصولية WCAG 2.1'),
                  _buildFeatureItem('🎭', 'تفاعلية محسنة مع تأثيرات بصرية'),
                  _buildFeatureItem('🌈', '16 ثيم لوني مختلف'),
                  _buildFeatureItem('📊', 'رسوم بيانية تفاعلية'),
                  _buildFeatureItem('⚡', 'أداء محسن وسرعة أكبر'),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _applyTheme(String themeKey) {
    // هنا يمكن تطبيق الثيم الجديد
    // في التطبيق الحقيقي، ستحتاج إلى استخدام Provider أو Bloc
    _showSnackBar(
        'تم تطبيق ثيم: ${AppColors.availableThemes[themeKey]!['name']}');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
