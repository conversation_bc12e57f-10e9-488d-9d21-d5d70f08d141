import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/index.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/models/currency.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/auth/services/login_settings_service.dart';

import '../../currencies/presenters/currency_presenter.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/custom_card.dart';
import '../../currencies/screens/currency_management_screen.dart';

import 'printer_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isArabic = true;
  // سيتم استخدام هذه المتغيرات في المستقبل عند تنفيذ وظائف متقدمة
  // final bool _showPromoBanner = true;
  // final bool _offlineMode = false;
  final String _selectedFont = 'Cairo';
  final Color _selectedColor = AppColors.primary;
  Currency? _defaultCurrency;
  bool _isLoadingCurrencies = false;

  // إعدادات تسجيل الدخول
  bool _rememberLogin = true;
  bool _autoLogoutEnabled = false;
  int _autoLogoutDuration = 60;

  @override
  void initState() {
    super.initState();
    // Usar addPostFrameCallback para evitar llamar a setState durante el build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDefaultCurrency();
      _loadLoginSettings();
    });
  }

  Future<void> _loadDefaultCurrency() async {
    setState(() {
      _isLoadingCurrencies = true;
    });

    try {
      final currencyPresenter =
          Provider.of<CurrencyPresenter>(context, listen: false);
      await currencyPresenter.loadCurrencies();
      setState(() {
        _defaultCurrency = currencyPresenter.defaultCurrency;
        _isLoadingCurrencies = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCurrencies = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل العملات: ${e.toString()}')),
        );
      }
    }
  }

  /// تحميل إعدادات تسجيل الدخول
  Future<void> _loadLoginSettings() async {
    try {
      final settings = await LoginSettingsService.getAllLoginSettings();
      setState(() {
        _rememberLogin = settings['remember_login'] ?? true;
        _autoLogoutEnabled = settings['auto_logout_enabled'] ?? false;
        _autoLogoutDuration = settings['auto_logout_duration'] ?? 60;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('فشل في تحميل إعدادات تسجيل الدخول: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'الإعدادات',
        showBackButton: true,
      ),
      // 🎨 خلفية محسنة متطابقة مع الداش بورد
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: AppColors.darkGradient, // نفس تدرج الداش بورد
                )
              : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.lightBackground,
                    AppColors.lightSurfaceVariant,
                  ],
                ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(20),
          children: [
            // 🎨 عنوان ترحيبي أنيق
            _buildWelcomeHeader(context),
            const SizedBox(height: 32),

            _buildAppearanceSection(),
            const SizedBox(height: 28),
            _buildGeneralSection(),
            const SizedBox(height: 28),
            _buildBusinessSection(),
            const SizedBox(height: 28),
            _buildLoginSettingsSection(),
            const SizedBox(height: 28),
            _buildUsersAndPermissionsSection(),
            const SizedBox(height: 28),
            _buildBackupSection(),
            const SizedBox(height: 28),
            _buildAboutSection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان ترحيبي أنيق وعصري
  Widget _buildWelcomeHeader(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        // نفس تصميم الإجراءات السريعة
        color: AppColors.primary.withValues(alpha: isDark ? 0.1 : 0.05),
        borderRadius: BorderRadius.circular(16), // نفس الزوايا
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // أيقونة دائرية مثل الإجراءات السريعة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: isDark ? 0.2 : 0.1),
              shape: BoxShape.circle, // دائرية مثل الإجراءات السريعة
            ),
            child: Icon(
              Icons.settings_outlined,
              color: AppColors.primary,
              size: 28, // نفس حجم أيقونات الإجراءات السريعة
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إعدادات التطبيق',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600, // نفس وزن خط الإجراءات السريعة
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'خصص تجربتك وإعداداتك المفضلة',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppearanceSection() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم بتصميم مثل الإجراءات السريعة
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: isDark ? 0.1 : 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // أيقونة دائرية مثل الإجراءات السريعة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      AppColors.primary.withValues(alpha: isDark ? 0.2 : 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.palette_outlined,
                  color: AppColors.primary,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'المظهر واللغة',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              Consumer<ThemeManager>(
                builder: (context, themeManager, child) {
                  return _buildSwitchTile(
                    title: 'الوضع الداكن',
                    subtitle: themeManager.getThemeModeDescription(),
                    value: themeManager.isDarkMode,
                    icon: themeManager.getThemeModeIcon(),
                    onChanged: (value) async {
                      if (value) {
                        await themeManager.setDarkMode();
                      } else {
                        await themeManager.setLightMode();
                      }
                    },
                  );
                },
              ),
              _buildDivider(),
              _buildSwitchTile(
                title: 'اللغة العربية',
                value: _isArabic,
                icon: Icons.language,
                onChanged: (value) {
                  setState(() {
                    _isArabic = value;
                  });
                  // TODO: Update locale
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGeneralSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
          child: Row(
            children: [
              const Icon(
                Icons.brush_outlined,
                color: AppColors.accent,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'التخصيص',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              _buildTappableTile(
                title: 'لون التطبيق',
                icon: Icons.palette_outlined,
                trailing: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _selectedColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: _selectedColor.withValues(alpha: 0.4),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
                onTap: () {
                  Navigator.pushNamed(context, '/theme_settings');
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'نوع الخط',
                icon: Icons.text_fields,
                trailing: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _selectedFont,
                    style: const AppTypography(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                onTap: () {
                  // TODO: Show font picker
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBusinessSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
          child: Row(
            children: [
              const Icon(
                Icons.business_outlined,
                color: AppColors.info,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'إعدادات العمل',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              _buildTappableTile(
                title: 'العملة الافتراضية',
                icon: Icons.monetization_on_outlined,
                trailing: _isLoadingCurrencies
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .primaryColor
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          _defaultCurrency?.name ?? 'غير محدد',
                          style: const AppTypography(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                onTap: () {
                  _handleCurrencySelection();
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'بيانات الملف الشخصي',
                icon: Icons.business_outlined,
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.companyProfile);
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'إعدادات الطابعة',
                icon: Icons.print_outlined,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PrinterSettingsScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم إعدادات تسجيل الدخول
  Widget _buildLoginSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
          child: Row(
            children: [
              const Icon(
                Icons.login_outlined,
                color: AppColors.success,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'إعدادات تسجيل الدخول',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              _buildSwitchTile(
                title: 'تذكر تسجيل الدخول',
                subtitle: 'عدم طلب تسجيل الدخول في كل مرة',
                value: _rememberLogin,
                icon: Icons.remember_me_outlined,
                onChanged: (value) async {
                  setState(() {
                    _rememberLogin = value;
                  });
                  await LoginSettingsService.setRememberLogin(value);
                },
              ),
              _buildDivider(),
              _buildSwitchTile(
                title: 'تسجيل الخروج التلقائي',
                subtitle: 'تسجيل خروج تلقائي بعد فترة عدم نشاط',
                value: _autoLogoutEnabled,
                icon: Icons.logout_outlined,
                onChanged: (value) async {
                  setState(() {
                    _autoLogoutEnabled = value;
                  });
                  await LoginSettingsService.setAutoLogoutEnabled(value);
                },
              ),
              if (_autoLogoutEnabled) ...[
                _buildDivider(),
                _buildTappableTile(
                  title: 'مدة عدم النشاط',
                  subtitle: _formatDurationText(_autoLogoutDuration),
                  icon: Icons.timer_outlined,
                  onTap: () => _showAutoLogoutDurationDialog(),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم المستخدمين والصلاحيات
  Widget _buildUsersAndPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
          child: Row(
            children: [
              const Icon(
                Icons.security_outlined,
                color: AppColors.warning,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'المستخدمين والصلاحيات',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              _buildTappableTile(
                title: 'إدارة المستخدمين',
                icon: Icons.people_outline,
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.usersManagement);
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'إدارة الأدوار والصلاحيات',
                icon: Icons.admin_panel_settings_outlined,
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.rolesManagement);
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'سجل النشاطات',
                icon: Icons.history_outlined,
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.activityLog);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBackupSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
          child: Row(
            children: [
              const Icon(
                Icons.cloud_outlined,
                color: AppColors.secondary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'السيرفر والنسخ الاحتياطي',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              _buildTappableTile(
                title: 'مسار النسخ الاحتياطي',
                subtitle: 'Google Drive',
                icon: Icons.cloud_outlined,
                onTap: () {
                  // TODO: Configure backup path
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'استعادة النسخة الاحتياطية',
                icon: Icons.restore,
                onTap: () {
                  // TODO: Restore backup
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'تهيئة البيانات التجريبية',
                icon: Icons.dataset_outlined,
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.sampleData);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
          child: Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.teal,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'معلومات التطبيق',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        CustomCard(
          elevation: 4,
          shadowColor: AppColors.primary.withValues(alpha: 0.2),
          child: Column(
            children: [
              _buildInfoTile(
                title: 'الإصدار',
                value: AppConstants.appVersion,
                icon: Icons.info_outline,
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'البحث عن تحديثات',
                icon: Icons.system_update_outlined,
                onTap: () {
                  // TODO: Check for updates
                },
              ),
              _buildDivider(),
              _buildTappableTile(
                title: 'سياسة الخصوصية',
                icon: Icons.privacy_tip_outlined,
                onTap: () {
                  // TODO: Show privacy policy
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// عرض حوار تحديد مدة تسجيل الخروج التلقائي
  Future<void> _showAutoLogoutDurationDialog() async {
    final List<int> predefinedDurations = [
      1,
      2,
      3,
      5,
      10,
      15,
      30,
      45,
      60,
      90,
      120,
      180,
      240
    ];

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.timer_outlined,
                color: AppColors.lightTextSecondary,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('مدة عدم النشاط'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'اختر المدة بالدقائق قبل تسجيل الخروج التلقائي:',
                  style: AppTypography(
                      fontSize: 14, color: AppColors.lightTextSecondary),
                ),
                const SizedBox(height: 16),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // الخيارات المحددة مسبقاً
                        ...predefinedDurations.map((duration) {
                          String displayText;
                          if (duration == 1) {
                            displayText = 'دقيقة واحدة';
                          } else if (duration == 2) {
                            displayText = 'دقيقتان';
                          } else if (duration <= 10) {
                            displayText = '$duration دقائق';
                          } else if (duration < 60) {
                            displayText = '$duration دقيقة';
                          } else if (duration == 60) {
                            displayText = 'ساعة واحدة';
                          } else if (duration == 120) {
                            displayText = 'ساعتان';
                          } else if (duration == 180) {
                            displayText = '3 ساعات';
                          } else {
                            displayText = '4 ساعات';
                          }

                          return RadioListTile<int>(
                            title: Text(displayText),
                            subtitle: duration <= 10
                                ? Text('$duration دقيقة',
                                    style: const AppTypography(
                                        fontSize: 12,
                                        color: AppColors.lightTextSecondary))
                                : null,
                            value: duration,
                            groupValue: _autoLogoutDuration,
                            dense: true,
                            onChanged: (value) {
                              Navigator.of(context).pop();
                              if (value != null) {
                                _updateAutoLogoutDuration(value);
                              }
                            },
                          );
                        }).toList(),

                        const Divider(),

                        // خيار مخصص
                        ListTile(
                          leading: const Icon(
                            Icons.edit_outlined,
                            color: AppColors.lightTextSecondary,
                          ),
                          title: const Text('مدة مخصصة'),
                          subtitle:
                              const Text('أدخل المدة التي تريدها بالدقائق'),
                          trailing:
                              const Icon(Icons.arrow_forward_ios, size: 16),
                          onTap: () {
                            Navigator.of(context).pop();
                            _showCustomDurationDialog();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  /// عرض حوار إدخال مدة مخصصة
  Future<void> _showCustomDurationDialog() async {
    final TextEditingController controller = TextEditingController();
    controller.text = _autoLogoutDuration.toString();

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: AppColors.lightTextSecondary,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('مدة مخصصة'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'أدخل المدة بالدقائق:',
                style: AppTypography(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              const Text(
                'الحد الأدنى: دقيقة واحدة\nالحد الأقصى: 1440 دقيقة (24 ساعة)',
                style: AppTypography(
                    fontSize: 12, color: AppColors.lightTextSecondary),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                keyboardType: TextInputType.number,
                autofocus: true,
                decoration: const InputDecoration(
                  labelText: 'المدة (دقيقة)',
                  hintText: 'مثال: 5',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(
                    Icons.timer_outlined,
                    color: AppColors.lightTextSecondary,
                  ),
                  suffixText: 'دقيقة',
                ),
                onSubmitted: (value) {
                  _processCustomDuration(value);
                  Navigator.of(context).pop();
                },
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: AppColors.info.withValues(alpha: 0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.lightTextSecondary,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'الرقم الواحد يعني دقيقة واحدة\nمثال: 5 = 5 دقائق، 120 = ساعتان',
                        style: AppTypography(
                          fontSize: 12,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                _processCustomDuration(controller.text);
                Navigator.of(context).pop();
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  /// معالجة المدة المخصصة المدخلة
  void _processCustomDuration(String input) {
    final int? duration = int.tryParse(input.trim());

    if (duration == null) {
      _showErrorSnackBar('يرجى إدخال رقم صحيح');
      return;
    }

    if (duration < 1) {
      _showErrorSnackBar('الحد الأدنى هو دقيقة واحدة');
      return;
    }

    if (duration > 1440) {
      _showErrorSnackBar('الحد الأقصى هو 1440 دقيقة (24 ساعة)');
      return;
    }

    _updateAutoLogoutDuration(duration);

    // عرض رسالة تأكيد
    String durationText;
    if (duration == 1) {
      durationText = 'دقيقة واحدة';
    } else if (duration == 2) {
      durationText = 'دقيقتان';
    } else if (duration <= 10) {
      durationText = '$duration دقائق';
    } else if (duration < 60) {
      durationText = '$duration دقيقة';
    } else if (duration == 60) {
      durationText = 'ساعة واحدة';
    } else if (duration == 120) {
      durationText = 'ساعتان';
    } else if (duration % 60 == 0) {
      final hours = duration ~/ 60;
      durationText = '$hours ساعة';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      durationText = '$hours ساعة و $minutes دقيقة';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث مدة تسجيل الخروج التلقائي إلى: $durationText'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// تنسيق نص المدة
  String _formatDurationText(int duration) {
    if (duration == 1) {
      return 'دقيقة واحدة';
    } else if (duration == 2) {
      return 'دقيقتان';
    } else if (duration <= 10) {
      return '$duration دقائق';
    } else if (duration < 60) {
      return '$duration دقيقة';
    } else if (duration == 60) {
      return 'ساعة واحدة';
    } else if (duration == 120) {
      return 'ساعتان';
    } else if (duration % 60 == 0) {
      final hours = duration ~/ 60;
      if (hours <= 10) {
        return '$hours ساعات';
      } else {
        return '$hours ساعة';
      }
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      return '$hours ساعة و $minutes دقيقة';
    }
  }

  /// تحديث مدة تسجيل الخروج التلقائي
  Future<void> _updateAutoLogoutDuration(int duration) async {
    setState(() {
      _autoLogoutDuration = duration;
    });
    await LoginSettingsService.setAutoLogoutDuration(duration);
  }

  Widget _buildSwitchTile({
    required String title,
    String? subtitle,
    required bool value,
    required IconData icon,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.lightTextSecondary,
        size: 26,
      ),
      title: Text(
        title,
        style: const AppTypography(fontWeight: FontWeight.w500),
      ),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildTappableTile({
    required String title,
    required IconData icon,
    String? subtitle,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.lightTextSecondary,
        size: 26,
      ),
      title: Text(
        title,
        style: const AppTypography(fontWeight: FontWeight.w500),
      ),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing ??
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: AppColors.primary.withValues(alpha: 0.7),
          ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.lightTextSecondary,
        size: 26,
      ),
      title: Text(
        title,
        style: const AppTypography(fontWeight: FontWeight.w500),
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          value,
          style: const AppTypography(
            color: AppColors.lightTextSecondary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return const Divider(
      height: 1,
      thickness: 1,
      indent: 70,
      endIndent: 16,
      color: AppColors.lightTextSecondary,
    );
  }

  // التعامل مع اختيار العملة
  Future<void> _handleCurrencySelection() async {
    final currencyPresenter =
        Provider.of<CurrencyPresenter>(context, listen: false);
    if (currencyPresenter.currencies.isEmpty) {
      await _loadDefaultCurrency();
    }

    if (!mounted) return;

    if (currencyPresenter.currencies.isNotEmpty) {
      _showCurrencySelectionDialog(currencyPresenter.currencies);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('لا توجد عملات متاحة. يرجى إضافة عملات أولاً.')),
      );

      // انتقل إلى شاشة إدارة العملات
      if (!mounted) return;
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const CurrencyManagementScreen(),
        ),
      ).then((_) => _loadDefaultCurrency());
    }
  }

  // عرض مربع حوار اختيار العملة
  void _showCurrencySelectionDialog(List<dynamic> currencies) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر العملة الافتراضية'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: currencies.length,
            itemBuilder: (context, index) {
              final currency = currencies[index];
              return ListTile(
                title: Text(currency.name),
                subtitle: Text('${currency.code} (${currency.symbol})'),
                trailing: currency.isDefault
                    ? const Icon(Icons.check, color: AppColors.success)
                    : null,
                onTap: () {
                  Navigator.pop(context);
                  _setDefaultCurrency(currency);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CurrencyManagementScreen(),
                ),
              ).then((_) => _loadDefaultCurrency());
            },
            child: const Text('إدارة العملات'),
          ),
        ],
      ),
    );
  }

  // تعيين العملة الافتراضية
  Future<void> _setDefaultCurrency(Currency currency) async {
    setState(() {
      _isLoadingCurrencies = true;
    });

    try {
      final currencyPresenter =
          Provider.of<CurrencyPresenter>(context, listen: false);
      final success = await currencyPresenter.setDefaultCurrency(currency.id);

      if (!mounted) return;

      if (success) {
        setState(() {
          _defaultCurrency = currency;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تعيين ${currency.name} كعملة افتراضية')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تعيين العملة الافتراضية')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('فشل في تعيين العملة الافتراضية: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingCurrencies = false;
        });
      }
    }
  }

  // ========== دوال مساعدة محسنة للتصميم العصري ==========

  /// بناء قسم حديث وأنيق
  Widget _buildModernSection({
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [
                  iconColor.withValues(alpha: 0.05),
                  iconColor.withValues(alpha: 0.02),
                ]
              : [
                  iconColor.withValues(alpha: 0.03),
                  iconColor.withValues(alpha: 0.01),
                ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: iconColor.withValues(alpha: 0.1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.shadowColor.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(children: children),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// بناء مفتاح تبديل حديث وأنيق
  Widget _buildModernSwitchTile({
    required String title,
    String? subtitle,
    required bool value,
    required IconData icon,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Switch.adaptive(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  /// بناء فاصل حديث وأنيق
  Widget _buildModernDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 1,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            Theme.of(context).dividerColor.withValues(alpha: 0.3),
            Colors.transparent,
          ],
        ),
      ),
    );
  }
}
