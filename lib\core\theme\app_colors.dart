import 'package:flutter/material.dart';
import 'dart:math' as math;

/// نظام الألوان الموحد لتطبيق تاجر بلس
/// يحتوي على جميع الألوان المستخدمة في الوضعين الفاتح والداكن
///
/// 🎨 الميزات:
/// - 8 ثيمات لونية مختلفة للاختيار
/// - دعم كامل للوضع الداكن والفاتح
/// - ألوان متخصصة لكل وحدة في النظام
/// - دوال مساعدة ذكية للحصول على الألوان المناسبة
/// - نظام تدرجات وظلال جميل
/// - ألوان متكيفة حسب السياق
class AppColors {
  // منع إنشاء كائن من هذا الكلاس
  AppColors._();

  // ========== الألوان الأساسية المحدثة مع Material Design 3 ==========

  /// اللون الشفاف - للخلفيات الشفافة والتراكبات
  /// 🌟 استخدم في: الخلفيات الشفافة، التراكبات، AppBar شفاف
  static const Color transparent = Colors.transparent;

  /// اللون الأساسي للتطبيق - أحمر تاجر بلس محدث وعصري
  /// 🔴 استخدم في: الأزرار الرئيسية، شريط التطبيق، العناصر المهمة
  /// مثال: ElevatedButton, AppBar, FloatingActionButton
  static const Color primary =
      Color(0xFFDC2626); // أحمر Material Design 3 - أكثر حيوية وعصرية

  /// نسخة داكنة من اللون الأساسي - محسنة للتباين والوصولية
  /// 🔴 استخدم في: حالة الضغط على الأزرار، الظلال الملونة، التفاعلات
  static const Color primaryDark =
      Color(0xFFB91C1C); // أحمر داكن متوازن ومتباين

  /// نسخة فاتحة من اللون الأساسي - ناعمة وجذابة
  /// 🔴 استخدم في: خلفيات فاتحة، حدود ملونة، تأثيرات بصرية، حالات التركيز
  static const Color primaryLight = Color(0xFFFECACA); // أحمر فاتح أنيق وناعم

  /// اللون الثانوي - رمادي أنيق ومتطور مع Material Design 3
  /// ⚫ استخدم في: النصوص الرئيسية، العناوين، الحدود الداكنة، العناصر الثانوية
  static const Color secondary = Color(0xFF374151); // رمادي مزرق عصري ومتوازن
  static const Color secondaryDark = Color(0xFF1F2937); // رمادي داكن عميق وأنيق
  static const Color secondaryLight =
      Color(0xFF6B7280); // رمادي فاتح متوازن ومريح

  /// اللون المميز - بنفسجي محدث وجذاب مع Material Design 3
  /// 🟣 استخدم في: العناصر المميزة، الإشعارات الخاصة، الأيقونات المهمة، التأكيدات
  static const Color accent = Color(0xFF8B5CF6); // بنفسجي عصري وأنيق ومشرق
  static const Color accentDark = Color(0xFF7C3AED); // بنفسجي داكن راقي وعميق
  static const Color accentLight = Color(0xFFDDD6FE); // بنفسجي فاتح جميل وناعم

  // ========== ألوان الحالة المحدثة مع Material Design 3 والوصولية ==========

  /// لون النجاح - أخضر طبيعي وحيوي مع تباين محسن
  /// ✅ استخدم في: رسائل النجاح، حالة مكتملة، أرباح، مبالغ موجبة، إشارات الموافقة
  /// مثال: SnackBar للنجاح، أيقونة التأكيد، حالة "مدفوع"، نجاح العمليات
  static const Color success =
      Color(0xFF16A34A); // أخضر Material Design 3 - طبيعي وواضح
  static const Color successLight =
      Color(0xFFDCFCE7); // خلفية فاتحة منعشة ومريحة
  static const Color successDark = Color(0xFF15803D); // أخضر داكن قوي ومتباين

  /// لون التحذير - برتقالي دافئ ومتوازن مع وضوح محسن
  /// ⚠️ استخدم في: رسائل التحذير، حالة انتظار، مبالغ مستحقة، تنبيهات مهمة
  /// مثال: تنبيهات المخزون، فواتير مستحقة، حالة "جزئي"، تحذيرات النظام
  static const Color warning =
      Color(0xFFEA580C); // برتقالي Material Design 3 - دافئ وواضح
  static const Color warningLight =
      Color(0xFFFED7AA); // خلفية فاتحة دافئة ومريحة
  static const Color warningDark = Color(0xFFDC2626); // برتقالي داكن قوي ومؤثر

  /// لون الخطأ - أحمر واضح ومؤثر مع تباين عالي
  /// ❌ استخدم في: رسائل الخطأ، حالة ملغاة، خسائر، مبالغ سالبة، أخطاء النظام
  /// مثال: رسائل الخطأ، حالة "ملغي"، أزرار الحذف، تنبيهات الخطر
  static const Color error =
      Color(0xFFDC2626); // أحمر Material Design 3 - واضح ومؤثر
  static const Color errorLight = Color(0xFFFECACA); // خلفية فاتحة ناعمة ومريحة
  static const Color errorDark = Color(0xFFB91C1C); // أحمر داكن قوي ومتباين

  /// لون المعلومات - أزرق هادئ ومهني مع وضوح محسن
  /// ℹ️ استخدم في: رسائل المعلومات، روابط، عناصر تفاعلية، إرشادات النظام
  /// مثال: أزرار المعلومات، روابط، حالة "قيد المراجعة"، نصائح المساعدة
  static const Color info =
      Color(0xFF2563EB); // أزرق Material Design 3 - مهني وهادئ
  static const Color infoLight = Color(0xFFDDEAFE); // خلفية فاتحة هادئة ومريحة
  static const Color infoDark = Color(0xFF1D4ED8); // أزرق داكن عميق ومتباين

  // ========== الوضع الفاتح المحدث مع Material Design 3 والوصولية ==========

  /// خلفية التطبيق الرئيسية - رمادي فاتح دافئ وعصري مع تباين محسن
  /// 🏠 استخدم في: خلفية الشاشات الرئيسية، Scaffold.backgroundColor، الخلفية العامة
  static const Color lightBackground =
      Color(0xFFFAFAFA); // رمادي فاتح دافئ ومريح للعين

  /// خلفية البطاقات والعناصر - أبيض نقي مع لمسة دافئة
  /// 📄 استخدم في: Card, Container, Dialog, BottomSheet، العناصر المرفوعة
  static const Color lightSurface = Color(0xFFFFFFFF); // أبيض نقي وواضح

  /// خلفية متغيرة للعناصر الثانوية - رمادي فاتح أنيق ومتباين
  /// 📋 استخدم في: ListTile غير نشط، خلفيات الأقسام، العناصر الثانوية
  static const Color lightSurfaceVariant =
      Color(0xFFF5F5F5); // رمادي فاتح أنيق ومتوازن

  /// ألوان النص على الخلفيات المختلفة - محدثة للتباين الأمثل والوصولية
  static const Color lightOnBackground =
      Color(0xFF111827); // رمادي داكن عميق ومتباين
  static const Color lightOnSurface =
      Color(0xFF1F2937); // رمادي داكن متوازن وواضح
  static const Color lightOnSurfaceVariant =
      Color(0xFF374151); // رمادي متوسط ومقروء

  // ========== ألوان النصوص في الوضع الفاتح المحدثة مع Material Design 3 ==========

  /// النص الأساسي - رمادي داكن عصري وواضح مع تباين عالي
  /// 📝 استخدم في: العناوين الرئيسية، النصوص المهمة، أسماء المنتجات، المحتوى الأساسي
  static const Color lightTextPrimary =
      Color(0xFF111827); // رمادي داكن عميق ومتباين

  /// النص الثانوي - رمادي متوسط متوازن ومقروء
  /// 📝 استخدم في: الوصف، التواريخ، المعلومات الإضافية، التسميات، النصوص الفرعية
  static const Color lightTextSecondary =
      Color(0xFF374151); // رمادي متوسط أنيق ومتوازن

  /// النص التوضيحي - رمادي فاتح ناعم ومريح
  /// 💭 استخدم في: placeholder في TextField، نصوص المساعدة، الإرشادات
  static const Color lightTextHint =
      Color(0xFF9CA3AF); // رمادي فاتح ناعم ومقروء

  /// النص المعطل - رمادي فاتح جداً مع وضوح مناسب
  /// 🚫 استخدم في: العناصر المعطلة، النصوص غير المتاحة، الحالات غير النشطة
  static const Color lightTextDisabled =
      Color(0xFFD1D5DB); // رمادي فاتح جداً ومتوازن

  // ========== ألوان الحدود والفواصل في الوضع الفاتح المحدثة مع Material Design 3 ==========

  /// خطوط الفصل - رمادي فاتح أنيق ومتباين
  /// ➖ استخدم في: Divider، خطوط الفصل بين الأقسام، الفواصل البصرية
  static const Color lightDivider =
      Color(0xFFE5E7EB); // رمادي فاتح أنيق ومتوازن

  /// حدود العناصر - رمادي فاتح متوازن وواضح
  /// 🔲 استخدم في: حدود TextField، Container، Card، العناصر التفاعلية
  static const Color lightBorder = Color(0xFFE5E7EB); // رمادي فاتح متوازن وواضح

  /// لون الظل - رمادي شفاف ناعم ومتطور
  /// 🌫️ استخدم في: BoxShadow، ظلال البطاقات والعناصر المرفوعة، التأثيرات البصرية
  static const Color lightShadow = Color(0x1A111827); // ظل رمادي ناعم ومتطور

  // ========== الوضع الداكن المحدث مع Material Design 3 والوصولية ==========

  /// خلفية التطبيق الداكنة - رمادي داكن عميق وأنيق مع راحة للعين
  /// 🌙 استخدم في: خلفية الشاشات الرئيسية، Scaffold.backgroundColor في الوضع الداكن
  static const Color darkBackground =
      Color(0xFF111827); // رمادي داكن عميق ومريح

  /// خلفية البطاقات والعناصر الداكنة - رمادي داكن متوازن ومتباين
  /// 🌙 استخدم في: Card, Container, Dialog, BottomSheet في الوضع الداكن
  static const Color darkSurface = Color(0xFF1F2937); // رمادي داكن متوازن وأنيق

  /// خلفية متغيرة للعناصر الثانوية الداكنة - رمادي داكن فاتح ومتباين
  /// 🌙 استخدم في: ListTile غير نشط، خلفيات الأقسام في الوضع الداكن
  static const Color darkSurfaceVariant =
      Color(0xFF374151); // رمادي داكن فاتح ومتوازن

  /// ألوان النص على الخلفيات الداكنة - محدثة للتباين الأمثل والوصولية
  static const Color darkOnBackground =
      Color(0xFFF9FAFB); // رمادي فاتح جداً ومشرق
  static const Color darkOnSurface = Color(0xFFF3F4F6); // رمادي فاتح وواضح
  static const Color darkOnSurfaceVariant =
      Color(0xFFE5E7EB); // رمادي فاتح متوسط ومقروء

  // ========== ألوان النصوص في الوضع الداكن المحدثة مع Material Design 3 ==========

  /// النص الأساسي الداكن - رمادي فاتح واضح ومريح للعين مع تباين عالي
  /// 🌙 استخدم في: العناوين الرئيسية، النصوص المهمة في الوضع الداكن
  static const Color darkTextPrimary =
      Color(0xFFF9FAFB); // رمادي فاتح جداً ومشرق

  /// النص الثانوي الداكن - رمادي فاتح متوسط ومقروء
  /// 🌙 استخدم في: الوصف، التواريخ، المعلومات الإضافية في الوضع الداكن
  static const Color darkTextSecondary =
      Color(0xFFE5E7EB); // رمادي فاتح متوسط ومتوازن

  /// النص التوضيحي الداكن - رمادي متوسط ناعم ومريح
  /// 🌙 استخدم في: placeholder في TextField، نصوص المساعدة في الوضع الداكن
  static const Color darkTextHint =
      Color(0xFF9CA3AF); // رمادي متوسط ناعم ومقروء

  /// النص المعطل الداكن - رمادي داكن متوازن
  /// 🌙 استخدم في: العناصر المعطلة، النصوص غير المتاحة في الوضع الداكن
  static const Color darkTextDisabled = Color(0xFF6B7280); // رمادي داكن متوازن

  // ========== ألوان الحدود والفواصل في الوضع الداكن المحدثة مع Material Design 3 ==========

  /// خطوط الفصل الداكنة - رمادي داكن ناعم ومتباين
  /// 🌙 استخدم في: Divider، خطوط الفصل بين الأقسام في الوضع الداكن
  static const Color darkDivider = Color(0xFF4B5563); // رمادي داكن ناعم ومتوازن

  /// حدود العناصر الداكنة - رمادي داكن متوازن وواضح
  /// 🌙 استخدم في: حدود TextField، Container، Card في الوضع الداكن
  static const Color darkBorder = Color(0xFF4B5563); // رمادي داكن متوازن وواضح

  /// لون الظل الداكن - أسود شفاف ناعم ومتطور
  /// 🌙 استخدم في: BoxShadow، ظلال البطاقات والعناصر المرفوعة في الوضع الداكن
  static const Color darkShadow = Color(0x40000000); // ظل أسود ناعم ومتطور

  // ========== ألوان خاصة بالتطبيق ==========

  /// ألوان البطاقات المالية
  static const Color cardProfit = success;
  static const Color cardLoss = error;
  static const Color cardNeutral = lightTextSecondary;

  /// ألوان الحالات
  static const Color statusActive = success;
  static const Color statusInactive = lightTextSecondary;
  static const Color statusPending = warning;
  static const Color statusCancelled = error;

  // ألوان إضافية للتوافق مع الكود الموجود
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onError = Color(0xFFFFFFFF);

  // ========== ألوان إضافية محدثة وعصرية ==========

  /// ألوان العنبر - أصفر ذهبي دافئ
  static const Color amber = Color(0xFFF6E05E); // أصفر ذهبي عصري
  static const Color amberLight = Color(0xFFFEF5E7); // خلفية فاتحة دافئة
  static const Color amberDark = Color(0xFFD69E2E); // أصفر ذهبي داكن

  /// ألوان التركوازي - أخضر مزرق هادئ
  static const Color teal = Color(0xFF319795); // تركوازي عصري
  static const Color tealLight = Color(0xFFB2F5EA); // خلفية فاتحة منعشة
  static const Color tealDark = Color(0xFF2C7A7B); // تركوازي داكن

  /// ألوان النيلي - أزرق عميق أنيق
  static const Color indigo = Color(0xFF4C51BF); // نيلي عميق
  static const Color indigoLight = Color(0xFFC3DAFE); // خلفية فاتحة هادئة
  static const Color indigoDark = Color(0xFF434190); // نيلي داكن

  /// ألوان البني - بني دافئ وطبيعي
  static const Color brown = Color(0xFF8B4513); // بني دافئ
  static const Color brownLight = Color(0xFFF7FAFC); // خلفية فاتحة ناعمة
  static const Color brownDark = Color(0xFF744210); // بني داكن

  /// ألوان البرتقالي العميق - برتقالي نابض بالحياة
  static const Color deepOrange = Color(0xFFFF6B35); // برتقالي نابض
  static const Color deepOrangeLight = Color(0xFFFED7CC); // خلفية فاتحة دافئة
  static const Color deepOrangeDark = Color(0xFFE55A2B); // برتقالي داكن

  // ألوان حالات القيود المحاسبية
  static const Color draft = lightTextSecondary;
  static const Color posted = success;
  static const Color voided = error;
  static const Color disabled = lightTextSecondary;

  // ========== ألوان الوحدات والأقسام ==========

  /// ألوان وحدات النظام المحدثة - لتمييز كل قسم بلون عصري ومتناسق

  /// 🔐 وحدة المصادقة وتسجيل الدخول - أزرق مهني هادئ
  /// استخدم في: شاشات تسجيل الدخول، إدارة المستخدمين، الصلاحيات
  static const Color moduleAuth = Color(0xFF3182CE); // أزرق مهني

  /// 👥 وحدة المستخدمين - أخضر طبيعي منعش
  /// استخدم في: قائمة المستخدمين، إضافة مستخدم، الأدوار
  static const Color moduleUsers = Color(0xFF38A169); // أخضر طبيعي

  /// 📦 وحدة المنتجات - بنفسجي أنيق وراقي
  /// استخدم في: كتالوج المنتجات، إضافة منتج، فئات المنتجات
  static const Color moduleProducts = Color(0xFF805AD5); // بنفسجي أنيق

  /// 💰 وحدة المبيعات - تركوازي عصري وهادئ
  /// استخدم في: فواتير المبيعات، نقاط البيع، العملاء
  static const Color moduleSales = Color(0xFF319795); // تركوازي عصري

  /// 🛒 وحدة المشتريات - برتقالي دافئ وودود
  /// استخدم في: فواتير المشتريات، الموردين، طلبات الشراء
  static const Color modulePurchases = Color(0xFFED8936); // برتقالي دافئ

  /// 📋 وحدة المخزون - بني أنيق ودافئ
  /// استخدم في: إدارة المخزون، المستودعات، جرد المخزون
  static const Color moduleInventory = Color(0xFF8B4513); // بني أنيق

  /// 💼 وحدة الحسابات - نيلي عميق وأنيق
  /// استخدم في: دليل الحسابات، القيود المحاسبية، التقارير المالية
  static const Color moduleAccounts = Color(0xFF4C51BF); // نيلي عميق

  /// 📊 وحدة التقارير - وردي جذاب ونابض
  /// استخدم في: جميع أنواع التقارير، الإحصائيات، الرسوم البيانية
  static const Color moduleReports = Color(0xFFED64A6); // وردي جذاب

  /// ⚙️ وحدة الإعدادات - رمادي مزرق عصري
  /// استخدم في: إعدادات التطبيق، التفضيلات، الإعدادات العامة
  static const Color moduleSettings = Color(0xFF718096); // رمادي مزرق

  // ========== ألوان أنواع الحسابات المحاسبية المحدثة ==========

  /// 🏢 الأصول - أزرق مهني هادئ (ما تملكه الشركة)
  /// استخدم في: النقدية، المخزون، الأثاث، السيارات، العقارات
  static const Color accountAsset = Color(0xFF3182CE); // أزرق مهني

  /// 📋 الخصوم - أحمر واضح ومؤثر (ما على الشركة من التزامات)
  /// استخدم في: القروض، الديون، المستحقات، الرواتب المستحقة
  static const Color accountLiability = Color(0xFFE53E3E); // أحمر واضح

  /// 👑 حقوق الملكية - بنفسجي أنيق وراقي (حقوق أصحاب الشركة)
  /// استخدم في: رأس المال، الأرباح المحتجزة، حسابات الشركاء
  static const Color accountEquity = Color(0xFF805AD5); // بنفسجي أنيق

  /// 💰 الإيرادات - أخضر طبيعي منعش (دخل الشركة)
  /// استخدم في: مبيعات، إيرادات خدمات، إيرادات أخرى
  static const Color accountRevenue = Color(0xFF38A169); // أخضر طبيعي

  /// 💸 المصروفات - برتقالي دافئ وودود (نفقات الشركة)
  /// استخدم في: الرواتب، الإيجار، الكهرباء، مصاريف التسويق
  static const Color accountExpense = Color(0xFFED8936); // برتقالي دافئ

  /// 👤 العملاء - تركوازي عصري وهادئ (حسابات العملاء)
  /// استخدم في: ذمم العملاء، حسابات العملاء الجارية
  static const Color accountCustomer = Color(0xFF319795); // تركوازي عصري

  /// 🏭 الموردين - بني أنيق ودافئ (حسابات الموردين)
  /// استخدم في: ذمم الموردين، حسابات الموردين الجارية
  static const Color accountSupplier = Color(0xFF8B4513); // بني أنيق

  /// 💵 النقدية - نيلي عميق وأنيق (الحسابات النقدية)
  /// استخدم في: الصندوق، البنك، الحسابات الجارية
  static const Color accountCash = Color(0xFF4C51BF); // نيلي عميق

  // ========== ألوان أنواع المعاملات المحدثة ==========

  /// 💰 الإيرادات - أخضر طبيعي منعش (دخل للشركة)
  /// استخدم في: قيود الإيرادات، المبالغ الواردة، الأرباح
  static const Color transactionIncome = Color(0xFF38A169); // أخضر طبيعي

  /// 💸 المصروفات - أحمر واضح ومؤثر (خروج من الشركة)
  /// استخدم في: قيود المصروفات، المبالغ الصادرة، التكاليف
  static const Color transactionExpense = Color(0xFFE53E3E); // أحمر واضح

  /// 🔄 التحويلات - أزرق مهني هادئ (نقل بين الحسابات)
  /// استخدم في: تحويل بين البنوك، نقل من الصندوق للبنك
  static const Color transactionTransfer = Color(0xFF3182CE); // أزرق مهني

  /// 🛍️ المبيعات - بنفسجي أنيق وراقي (عمليات البيع)
  /// استخدم في: فواتير المبيعات، إيصالات البيع
  static const Color transactionSale = Color(0xFF805AD5); // بنفسجي أنيق

  /// 🛒 المشتريات - برتقالي دافئ وودود (عمليات الشراء)
  /// استخدم في: فواتير المشتريات، إيصالات الشراء
  static const Color transactionPurchase = Color(0xFFED8936); // برتقالي دافئ

  // ========== ألوان مستويات الوصول والصلاحيات ==========

  /// 🚫 عدم الوصول - رمادي (لا يوجد صلاحية)
  /// استخدم في: المستخدمين بدون صلاحيات، الميزات المقفلة
  static const Color accessNone = lightTextSecondary;

  /// 👁️ العرض فقط - أزرق (قراءة بدون تعديل)
  /// استخدم في: صلاحية المشاهدة، التقارير للمستخدمين العاديين
  static const Color accessView = info;

  /// ✏️ التعديل - برتقالي (قراءة وتعديل)
  /// استخدم في: صلاحية التعديل، المستخدمين المتوسطين
  static const Color accessEdit = warning;

  /// 🔓 الوصول الكامل - أخضر (جميع الصلاحيات)
  /// استخدم في: صلاحية المدير، الوصول الكامل للنظام
  static const Color accessFull = success;

  // ========== ألوان حالات المزامنة ==========

  /// ✅ مزامن - أخضر (تم التحديث بنجاح)
  /// استخدم في: البيانات المحدثة، المزامنة الناجحة
  static const Color syncSynced = success;

  /// ⏳ في الانتظار - برتقالي (قيد المزامنة)
  /// استخدم في: البيانات قيد التحديث، انتظار الاتصال
  static const Color syncPending = warning;

  /// ❌ فشل - أحمر (خطأ في المزامنة)
  /// استخدم في: فشل التحديث، أخطاء الشبكة
  static const Color syncFailed = error;

  /// 📴 غير متصل - رمادي (لا يوجد اتصال)
  /// استخدم في: وضع عدم الاتصال، البيانات المحلية فقط
  static const Color syncOffline = lightTextSecondary;

  // ========== ألوان مستويات الأولوية ==========

  /// 🟢 أولوية منخفضة - أخضر (غير عاجل)
  /// استخدم في: المهام العادية، التذكيرات البسيطة
  static const Color priorityLow = success;

  /// 🟡 أولوية متوسطة - برتقالي (مهم نسبياً)
  /// استخدم في: المهام المهمة، التنبيهات المتوسطة
  static const Color priorityMedium = warning;

  /// 🔴 أولوية عالية - أحمر (مهم جداً)
  /// استخدم في: المهام الحرجة، التنبيهات المهمة
  static const Color priorityHigh = error;

  /// 🟣 أولوية عاجلة - بنفسجي (فوري)
  /// استخدم في: الطوارئ، المهام الفورية، التنبيهات الحرجة
  static const Color priorityUrgent = accent;

  /// ألوان التدرج المحدثة للخلفيات الجذابة والعصرية
  static const List<Color> primaryGradient = [
    Color(0xFFE53E3E), // أحمر عصري
    Color(0xFFC53030), // أحمر داكن
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF2D3748), // رمادي عصري
    Color(0xFF1A202C), // رمادي داكن
  ];

  static const List<Color> successGradient = [
    Color(0xFF38A169), // أخضر طبيعي
    Color(0xFF2F855A), // أخضر داكن
  ];

  static const List<Color> darkGradient = [
    Color(0xFF2D3748), // رمادي داكن عصري
    Color(0xFF1A202C), // رمادي داكن عميق
  ];

  /// تدرجات جديدة وعصرية
  static const List<Color> infoGradient = [
    Color(0xFF3182CE), // أزرق مهني
    Color(0xFF2C5282), // أزرق داكن
  ];

  static const List<Color> warningGradient = [
    Color(0xFFED8936), // برتقالي دافئ
    Color(0xFFDD6B20), // برتقالي داكن
  ];

  static const List<Color> accentGradient = [
    Color(0xFF805AD5), // بنفسجي أنيق
    Color(0xFF553C9A), // بنفسجي داكن
  ];

  // ========== ألوان قابلة للتخصيص ==========

  /// مجموعة الألوان المتاحة للمستخدم - محدثة وعصرية مع Material Design 3
  static const Map<String, Map<String, dynamic>> availableThemes = {
    'red': {
      'name': 'أحمر تاجر بلس العصري',
      'primary':
          Color(0xFFDC2626), // أحمر Material Design 3 - أكثر حيوية وعصرية
      'primaryDark': Color(0xFFB91C1C), // أحمر داكن متوازن
      'primaryLight': Color(0xFFFECACA), // أحمر فاتح أنيق
      'secondary': Color(0xFF7F1D1D), // ثانوي أحمر داكن
      'secondaryLight': Color(0xFFFEE2E2), // ثانوي فاتح
      'accent': Color(0xFFEF4444), // أكسنت أحمر مشرق
      'accentLight': Color(0xFFFECDD3), // أكسنت فاتح
      'gradient': [Color(0xFFDC2626), Color(0xFFB91C1C)],
      'surface': Color(0xFFFEF2F2), // سطح فاتح مع لمسة حمراء
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'blue': {
      'name': 'أزرق مهني هادئ',
      'primary': Color(0xFF2563EB), // أزرق Material Design 3 - مهني وحديث
      'primaryDark': Color(0xFF1D4ED8), // أزرق داكن عميق
      'primaryLight': Color(0xFFDDEAFE), // أزرق فاتح هادئ
      'secondary': Color(0xFF1E40AF), // ثانوي أزرق داكن
      'secondaryLight': Color(0xFFE0E7FF), // ثانوي فاتح
      'accent': Color(0xFF3B82F6), // أكسنت أزرق مشرق
      'accentLight': Color(0xFFBFDBFE), // أكسنت فاتح
      'gradient': [Color(0xFF2563EB), Color(0xFF1D4ED8)],
      'surface': Color(0xFFF8FAFC), // سطح فاتح مع لمسة زرقاء
      'onSurface': Color(0xFF1E293B), // نص على السطح
    },
    'green': {
      'name': 'أخضر طبيعي منعش',
      'primary': Color(0xFF16A34A), // أخضر Material Design 3 - طبيعي ومنعش
      'primaryDark': Color(0xFF15803D), // أخضر داكن قوي
      'primaryLight': Color(0xFFDCFCE7), // أخضر فاتح منعش
      'secondary': Color(0xFF166534), // ثانوي أخضر داكن
      'secondaryLight': Color(0xFFF0FDF4), // ثانوي فاتح
      'accent': Color(0xFF22C55E), // أكسنت أخضر مشرق
      'accentLight': Color(0xFFBBF7D0), // أكسنت فاتح
      'gradient': [Color(0xFF16A34A), Color(0xFF15803D)],
      'surface': Color(0xFFF7FEF9), // سطح فاتح مع لمسة خضراء
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'purple': {
      'name': 'بنفسجي أنيق وراقي',
      'primary': Color(0xFF9333EA), // بنفسجي Material Design 3 - أنيق وعصري
      'primaryDark': Color(0xFF7C3AED), // بنفسجي داكن راقي
      'primaryLight': Color(0xFFE9D5FF), // بنفسجي فاتح أنيق
      'secondary': Color(0xFF6B21A8), // ثانوي بنفسجي داكن
      'secondaryLight': Color(0xFFF3E8FF), // ثانوي فاتح
      'accent': Color(0xFFA855F7), // أكسنت بنفسجي مشرق
      'accentLight': Color(0xFFDDD6FE), // أكسنت فاتح
      'gradient': [Color(0xFF9333EA), Color(0xFF7C3AED)],
      'surface': Color(0xFFFAF5FF), // سطح فاتح مع لمسة بنفسجية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'orange': {
      'name': 'برتقالي دافئ وودود',
      'primary': Color(0xFFEA580C), // برتقالي Material Design 3 - دافئ وودود
      'primaryDark': Color(0xFFDC2626), // برتقالي داكن قوي
      'primaryLight': Color(0xFFFED7AA), // برتقالي فاتح دافئ
      'secondary': Color(0xFFC2410C), // ثانوي برتقالي داكن
      'secondaryLight': Color(0xFFFFF7ED), // ثانوي فاتح
      'accent': Color(0xFFF97316), // أكسنت برتقالي مشرق
      'accentLight': Color(0xFFFFEDD5), // أكسنت فاتح
      'gradient': [Color(0xFFEA580C), Color(0xFFDC2626)],
      'surface': Color(0xFFFFFBF5), // سطح فاتح مع لمسة برتقالية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'teal': {
      'name': 'تركوازي عصري وهادئ',
      'primary': Color(0xFF0D9488), // تركوازي Material Design 3 - عصري وهادئ
      'primaryDark': Color(0xFF0F766E), // تركوازي داكن عميق
      'primaryLight': Color(0xFFCCFBF1), // تركوازي فاتح منعش
      'secondary': Color(0xFF134E4A), // ثانوي تركوازي داكن
      'secondaryLight': Color(0xFFF0FDFA), // ثانوي فاتح
      'accent': Color(0xFF14B8A6), // أكسنت تركوازي مشرق
      'accentLight': Color(0xFFB2F5EA), // أكسنت فاتح
      'gradient': [Color(0xFF0D9488), Color(0xFF0F766E)],
      'surface': Color(0xFFF7FFFE), // سطح فاتح مع لمسة تركوازية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'indigo': {
      'name': 'نيلي عميق وأنيق',
      'primary': Color(0xFF4F46E5), // نيلي Material Design 3 - عميق وأنيق
      'primaryDark': Color(0xFF4338CA), // نيلي داكن راقي
      'primaryLight': Color(0xFFE0E7FF), // نيلي فاتح هادئ
      'secondary': Color(0xFF3730A3), // ثانوي نيلي داكن
      'secondaryLight': Color(0xFFF0F9FF), // ثانوي فاتح
      'accent': Color(0xFF6366F1), // أكسنت نيلي مشرق
      'accentLight': Color(0xFFC7D2FE), // أكسنت فاتح
      'gradient': [Color(0xFF4F46E5), Color(0xFF4338CA)],
      'surface': Color(0xFFFAFAFF), // سطح فاتح مع لمسة نيلية
      'onSurface': Color(0xFF1E293B), // نص على السطح
    },
    'pink': {
      'name': 'وردي جذاب ونابض',
      'primary': Color(0xFFEC4899), // وردي Material Design 3 - جذاب ونابض
      'primaryDark': Color(0xFFDB2777), // وردي داكن أنيق
      'primaryLight': Color(0xFFFCE7F3), // وردي فاتح ناعم
      'secondary': Color(0xFFBE185D), // ثانوي وردي داكن
      'secondaryLight': Color(0xFFFDF2F8), // ثانوي فاتح
      'accent': Color(0xFFF472B6), // أكسنت وردي مشرق
      'accentLight': Color(0xFFF9A8D4), // أكسنت فاتح
      'gradient': [Color(0xFFEC4899), Color(0xFFDB2777)],
      'surface': Color(0xFFFEF7FF), // سطح فاتح مع لمسة وردية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    // ========== ثيمات إضافية محسنة مع Material Design 3 ==========
    'emerald': {
      'name': 'زمردي فاخر ومميز',
      'primary': Color(0xFF059669), // زمردي Material Design 3 - فاخر ومميز
      'primaryDark': Color(0xFF047857), // زمردي داكن عميق
      'primaryLight': Color(0xFFD1FAE5), // زمردي فاتح منعش
      'secondary': Color(0xFF065F46), // ثانوي زمردي داكن
      'secondaryLight': Color(0xFFECFDF5), // ثانوي فاتح
      'accent': Color(0xFF10B981), // أكسنت زمردي مشرق
      'accentLight': Color(0xFFA7F3D0), // أكسنت فاتح
      'gradient': [Color(0xFF059669), Color(0xFF047857)],
      'surface': Color(0xFFF0FDF4), // سطح فاتح مع لمسة زمردية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'cyan': {
      'name': 'سماوي منعش وحيوي',
      'primary': Color(0xFF0891B2), // سماوي Material Design 3 - منعش وحيوي
      'primaryDark': Color(0xFF0E7490), // سماوي داكن عميق
      'primaryLight': Color(0xFFCFFAFE), // سماوي فاتح منعش
      'secondary': Color(0xFF155E75), // ثانوي سماوي داكن
      'secondaryLight': Color(0xFFECFEFF), // ثانوي فاتح
      'accent': Color(0xFF06B6D4), // أكسنت سماوي مشرق
      'accentLight': Color(0xFFA5F3FC), // أكسنت فاتح
      'gradient': [Color(0xFF0891B2), Color(0xFF0E7490)],
      'surface': Color(0xFFF0FDFF), // سطح فاتح مع لمسة سماوية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'violet': {
      'name': 'بنفسجي عميق وأنيق',
      'primary': Color(0xFF7C3AED), // بنفسجي Material Design 3 - عميق وأنيق
      'primaryDark': Color(0xFF6D28D9), // بنفسجي داكن راقي
      'primaryLight': Color(0xFFDDD6FE), // بنفسجي فاتح أنيق
      'secondary': Color(0xFF5B21B6), // ثانوي بنفسجي داكن
      'secondaryLight': Color(0xFFF5F3FF), // ثانوي فاتح
      'accent': Color(0xFF8B5CF6), // أكسنت بنفسجي مشرق
      'accentLight': Color(0xFFC4B5FD), // أكسنت فاتح
      'gradient': [Color(0xFF7C3AED), Color(0xFF6D28D9)],
      'surface': Color(0xFFFAF5FF), // سطح فاتح مع لمسة بنفسجية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'rose': {
      'name': 'وردي راقي ومميز',
      'primary': Color(0xFFE11D48), // وردي Material Design 3 - راقي ومميز
      'primaryDark': Color(0xFFBE123C), // وردي داكن أنيق
      'primaryLight': Color(0xFFFECDD3), // وردي فاتح ناعم
      'secondary': Color(0xFF9F1239), // ثانوي وردي داكن
      'secondaryLight': Color(0xFFFFF1F2), // ثانوي فاتح
      'accent': Color(0xFFF43F5E), // أكسنت وردي مشرق
      'accentLight': Color(0xFFFDA4AF), // أكسنت فاتح
      'gradient': [Color(0xFFE11D48), Color(0xFFBE123C)],
      'surface': Color(0xFFFFF7F8), // سطح فاتح مع لمسة وردية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'amber': {
      'name': 'كهرماني دافئ وجذاب',
      'primary': Color(0xFFD97706), // كهرماني Material Design 3 - دافئ وجذاب
      'primaryDark': Color(0xFFB45309), // كهرماني داكن قوي
      'primaryLight': Color(0xFFFEF3C7), // كهرماني فاتح دافئ
      'secondary': Color(0xFF92400E), // ثانوي كهرماني داكن
      'secondaryLight': Color(0xFFFFFBEB), // ثانوي فاتح
      'accent': Color(0xFFF59E0B), // أكسنت كهرماني مشرق
      'accentLight': Color(0xFFFDE68A), // أكسنت فاتح
      'gradient': [Color(0xFFD97706), Color(0xFFB45309)],
      'surface': Color(0xFFFFFDF7), // سطح فاتح مع لمسة كهرمانية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'lime': {
      'name': 'أخضر ليموني منعش',
      'primary': Color(0xFF65A30D), // ليموني Material Design 3 - منعش وحيوي
      'primaryDark': Color(0xFF4D7C0F), // ليموني داكن قوي
      'primaryLight': Color(0xFFECFCCB), // ليموني فاتح منعش
      'secondary': Color(0xFF365314), // ثانوي ليموني داكن
      'secondaryLight': Color(0xFFF7FEE7), // ثانوي فاتح
      'accent': Color(0xFF84CC16), // أكسنت ليموني مشرق
      'accentLight': Color(0xFFD9F99D), // أكسنت فاتح
      'gradient': [Color(0xFF65A30D), Color(0xFF4D7C0F)],
      'surface': Color(0xFFFEFFFE), // سطح فاتح مع لمسة ليمونية
      'onSurface': Color(0xFF1F2937), // نص على السطح
    },
    'sky': {
      'name': 'أزرق سماوي هادئ',
      'primary': Color(0xFF0284C7), // سماوي Material Design 3 - هادئ ومنعش
      'primaryDark': Color(0xFF0369A1), // سماوي داكن عميق
      'primaryLight': Color(0xFFE0F2FE), // سماوي فاتح هادئ
      'secondary': Color(0xFF075985), // ثانوي سماوي داكن
      'secondaryLight': Color(0xFFF0F9FF), // ثانوي فاتح
      'accent': Color(0xFF0EA5E9), // أكسنت سماوي مشرق
      'accentLight': Color(0xFFBAE6FD), // أكسنت فاتح
      'gradient': [Color(0xFF0284C7), Color(0xFF0369A1)],
      'surface': Color(0xFFF8FCFF), // سطح فاتح مع لمسة سماوية
      'onSurface': Color(0xFF1E293B), // نص على السطح
    },
    // ========== ثيم ليلي خاص للوضع الداكن ==========
    'midnight': {
      'name': 'ليلي أنيق وراقي',
      'primary': Color(0xFF6366F1), // بنفسجي ليلي أنيق للوضع الداكن
      'primaryDark': Color(0xFF4F46E5), // أغمق للتباين
      'primaryLight': Color(0xFF818CF8), // أفتح للعناصر الثانوية
      'secondary': Color(0xFF374151), // رمادي ليلي متوازن
      'secondaryLight': Color(0xFF4B5563), // رمادي فاتح
      'accent': Color(0xFF8B5CF6), // أكسنت بنفسجي مشرق
      'accentLight': Color(0xFFA78BFA), // أكسنت فاتح
      'gradient': [Color(0xFF6366F1), Color(0xFF4F46E5)],
      'surface': Color(0xFF1F2937), // سطح داكن أنيق
      'onSurface': Color(0xFFF9FAFB), // نص فاتح على السطح الداكن
    },
  };

  // ========== دوال مساعدة ذكية ==========

  /// الحصول على لون النص المناسب حسب لون الخلفية (ذكي) مع ضمان الوصولية WCAG 2.1
  static Color getTextColorForBackground(Color backgroundColor) {
    final whiteContrast = calculateContrast(backgroundColor, Colors.white);
    final blackContrast =
        calculateContrast(backgroundColor, const Color(0xFF1F2937));

    // ضمان تباين لا يقل عن 4.5:1 للنصوص العادية (WCAG AA)
    if (whiteContrast >= 4.5 && whiteContrast > blackContrast) {
      return Colors.white;
    } else if (blackContrast >= 4.5) {
      return const Color(0xFF1F2937);
    } else {
      // في حالة عدم تحقق التباين المطلوب، اختر الأفضل
      return whiteContrast > blackContrast
          ? Colors.white
          : const Color(0xFF1F2937);
    }
  }

  /// الحصول على لون الأيقونة المناسب حسب لون الخلفية (ذكي)
  static Color getIconColorForBackground(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    if (luminance > 0.6) {
      return lightTextSecondary;
    } else {
      return darkTextSecondary;
    }
  }

  /// الحصول على لون الخلفية المتكيف حسب الثيم
  static Color getAdaptiveCardBackground(bool isDark) {
    return isDark ? darkSurface : lightSurface;
  }

  /// الحصول على لون النص المتكيف حسب الثيم
  static Color getAdaptiveTextColor(bool isDark) {
    return isDark ? darkTextPrimary : lightTextPrimary;
  }

  /// الحصول على لون النص الثانوي المتكيف
  static Color getAdaptiveSecondaryTextColor(bool isDark) {
    return isDark ? darkTextSecondary : lightTextSecondary;
  }

  /// الحصول على لون الحدود المتكيف
  static Color getAdaptiveBorderColor(bool isDark) {
    return isDark ? darkBorder : lightBorder;
  }

  /// الحصول على لون الظل المتكيف
  static Color getAdaptiveShadowColor(bool isDark) {
    return isDark ? darkShadow : lightShadow;
  }

  /// الحصول على لون نوع الحساب
  static Color getAccountTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'asset':
      case 'أصول':
        return accountAsset;
      case 'liability':
      case 'خصوم':
        return accountLiability;
      case 'equity':
      case 'حقوق الملكية':
        return accountEquity;
      case 'revenue':
      case 'إيرادات':
        return accountRevenue;
      case 'expense':
      case 'مصروفات':
        return accountExpense;
      case 'customer':
      case 'عملاء':
        return accountCustomer;
      case 'supplier':
      case 'موردين':
        return accountSupplier;
      case 'cash':
      case 'نقدية':
        return accountCash;
      default:
        return lightTextSecondary;
    }
  }

  /// فحص التباين بين لونين
  static double getContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// التأكد من التباين الكافي للنص
  static Color ensureTextContrast(Color textColor, Color backgroundColor) {
    final contrast = getContrastRatio(textColor, backgroundColor);
    if (contrast < 4.5) {
      // إذا كان التباين ضعيف، نغير لون النص
      return getTextColorForBackground(backgroundColor);
    }
    return textColor;
  }

  /// إنشاء تدرج لوني
  static LinearGradient createGradient(
    List<Color> colors, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
    );
  }

  /// إنشاء ظل ملون
  static List<BoxShadow> createSoftShadow({
    Color? color,
    double blurRadius = 8.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
    double opacity = 0.15,
  }) {
    return [
      BoxShadow(
        color: (color ?? lightTextPrimary).withValues(alpha: opacity),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }

  /// الحصول على لون الوحدة حسب الاسم
  static Color getModuleColor(String module) {
    switch (module.toLowerCase()) {
      case 'auth':
      case 'authentication':
      case 'المصادقة':
        return moduleAuth;
      case 'users':
      case 'المستخدمين':
        return moduleUsers;
      case 'products':
      case 'المنتجات':
        return moduleProducts;
      case 'sales':
      case 'المبيعات':
        return moduleSales;
      case 'purchases':
      case 'المشتريات':
        return modulePurchases;
      case 'inventory':
      case 'المخزون':
        return moduleInventory;
      case 'accounts':
      case 'الحسابات':
        return moduleAccounts;
      case 'reports':
      case 'التقارير':
        return moduleReports;
      case 'settings':
      case 'الإعدادات':
        return moduleSettings;
      default:
        return lightTextSecondary;
    }
  }

  /// الحصول على لون نوع المعاملة
  static Color getTransactionTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'income':
      case 'إيراد':
        return transactionIncome;
      case 'expense':
      case 'مصروف':
        return transactionExpense;
      case 'transfer':
      case 'تحويل':
        return transactionTransfer;
      case 'sale':
      case 'مبيعات':
        return transactionSale;
      case 'purchase':
      case 'مشتريات':
        return transactionPurchase;
      default:
        return lightTextSecondary;
    }
  }

  /// الحصول على لون مستوى الوصول
  static Color getAccessLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'none':
      case 'لا يوجد':
        return accessNone;
      case 'view':
      case 'عرض':
        return accessView;
      case 'edit':
      case 'تعديل':
        return accessEdit;
      case 'full':
      case 'كامل':
        return accessFull;
      default:
        return accessNone;
    }
  }

  /// الحصول على لون حالة المزامنة
  static Color getSyncStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'synced':
      case 'مزامن':
        return syncSynced;
      case 'pending':
      case 'انتظار':
        return syncPending;
      case 'failed':
      case 'فشل':
        return syncFailed;
      case 'offline':
      case 'غير متصل':
        return syncOffline;
      default:
        return syncOffline;
    }
  }

  /// الحصول على لون الأولوية
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
      case 'منخفضة':
        return priorityLow;
      case 'medium':
      case 'متوسطة':
        return priorityMedium;
      case 'high':
      case 'عالية':
        return priorityHigh;
      case 'urgent':
      case 'عاجلة':
        return priorityUrgent;
      default:
        return priorityMedium;
    }
  }

  // ========== دوال مساعدة جديدة للحالات المتقدمة ==========

  /// الحصول على لون حالة الشبكة
  static Color getNetworkStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'connected':
      case 'متصل':
        return networkConnected;
      case 'disconnected':
      case 'منقطع':
        return networkDisconnected;
      case 'connecting':
      case 'يتصل':
        return networkConnecting;
      default:
        return networkUnknown;
    }
  }

  /// الحصول على لون حالة البطارية
  static Color getBatteryStatusColor(double percentage) {
    if (percentage >= 80) {
      return batteryFull;
    } else if (percentage >= 40) {
      return batteryMedium;
    } else {
      return batteryLow;
    }
  }

  /// الحصول على لون مستوى الأمان
  static Color getSecurityLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'high':
      case 'عالي':
        return securityHigh;
      case 'medium':
      case 'متوسط':
        return securityMedium;
      case 'low':
      case 'منخفض':
        return securityLow;
      default:
        return securityUnknown;
    }
  }

  /// الحصول على لون مستوى الأداء
  static Color getPerformanceLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'excellent':
      case 'ممتاز':
        return performanceExcellent;
      case 'good':
      case 'جيد':
        return performanceGood;
      case 'average':
      case 'متوسط':
        return performanceAverage;
      case 'poor':
      case 'ضعيف':
        return performancePoor;
      default:
        return performanceAverage;
    }
  }

  /// إنشاء تدرج لوني ديناميكي
  static LinearGradient createDynamicGradient(
    Color startColor,
    Color endColor, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
      stops: stops,
    );
  }

  /// إنشاء تدرج لوني متعدد الألوان
  static LinearGradient createMultiColorGradient(
    List<Color> colors, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: stops,
    );
  }

  // ========== نظام الشفافية العصري والذكي مع Material Design 3 ==========

  /// إنشاء لون بشفافية مخصصة - طريقة عصرية وآمنة
  static Color withCustomOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity.clamp(0.0, 1.0));
  }

  /// مستويات التأكيد النصي العصرية (Material Design 3) - محسنة للوصولية
  static Color get textEmphasisHigh => lightTextPrimary.withValues(alpha: 0.90);
  static Color get textEmphasisMedium =>
      lightTextPrimary.withValues(alpha: 0.70);
  static Color get textEmphasisDisabled =>
      lightTextPrimary.withValues(alpha: 0.40);

  /// طبقات التفاعل العصرية للوضع الفاتح - محسنة للتفاعل الحديث
  static Color get surfaceOverlay => lightTextPrimary.withValues(alpha: 0.04);
  static Color get hoverOverlay => lightTextPrimary.withValues(alpha: 0.08);
  static Color get focusOverlay => lightTextPrimary.withValues(alpha: 0.12);
  static Color get pressedOverlay => lightTextPrimary.withValues(alpha: 0.16);
  static Color get selectedOverlay => lightTextPrimary.withValues(alpha: 0.12);
  static Color get draggedOverlay => lightTextPrimary.withValues(alpha: 0.16);

  /// مستويات التأكيد للوضع الداكن - محسنة للوصولية
  static Color get darkTextEmphasisHigh =>
      darkTextPrimary.withValues(alpha: 0.90);
  static Color get darkTextEmphasisMedium =>
      darkTextPrimary.withValues(alpha: 0.70);
  static Color get darkTextEmphasisDisabled =>
      darkTextPrimary.withValues(alpha: 0.40);

  /// طبقات التفاعل للوضع الداكن - محسنة للتفاعل الحديث
  static Color get darkSurfaceOverlay =>
      darkTextPrimary.withValues(alpha: 0.04);
  static Color get darkHoverOverlay => darkTextPrimary.withValues(alpha: 0.08);
  static Color get darkFocusOverlay => darkTextPrimary.withValues(alpha: 0.12);
  static Color get darkPressedOverlay =>
      darkTextPrimary.withValues(alpha: 0.16);
  static Color get darkSelectedOverlay =>
      darkTextPrimary.withValues(alpha: 0.12);
  static Color get darkDraggedOverlay =>
      darkTextPrimary.withValues(alpha: 0.16);

  // ========== ألوان التفاعل المتقدمة للعناصر المختلفة ==========

  /// ألوان التفاعل للأزرار الأساسية
  static Color primaryHover(Color baseColor) =>
      baseColor.withValues(alpha: 0.9);
  static Color primaryPressed(Color baseColor) =>
      baseColor.withValues(alpha: 0.8);
  static Color primaryFocused(Color baseColor) =>
      baseColor.withValues(alpha: 0.12);

  /// ألوان التفاعل للأزرار الثانوية
  static Color secondaryHover(Color baseColor) =>
      baseColor.withValues(alpha: 0.08);
  static Color secondaryPressed(Color baseColor) =>
      baseColor.withValues(alpha: 0.16);
  static Color secondaryFocused(Color baseColor) =>
      baseColor.withValues(alpha: 0.12);

  /// ألوان التفاعل للبطاقات والأسطح
  static Color surfaceHover(Color baseColor) =>
      baseColor.withValues(alpha: 0.04);
  static Color surfacePressed(Color baseColor) =>
      baseColor.withValues(alpha: 0.08);
  static Color surfaceSelected(Color baseColor) =>
      baseColor.withValues(alpha: 0.12);

  // ========== ألوان إضافية مطلوبة للنظام ==========

  static Color get primaryContainer => primaryLight.withValues(alpha: 0.12);
  static Color get onPrimaryContainer => primaryDark;
  static Color get secondaryContainer => secondaryLight.withValues(alpha: 0.12);
  static Color get onSecondaryContainer => secondaryDark;
  static Color get errorContainer => errorLight.withValues(alpha: 0.12);
  static Color get onErrorContainer => errorDark;
  static Color get surfaceContainer => lightSurfaceVariant;
  static Color get surfaceContainerHighest => lightSurfaceVariant;
  static Color get outline => lightBorder;
  static Color get outlineVariant => lightBorder.withValues(alpha: 0.5);
  static Color get scrim => lightTextPrimary.withValues(alpha: 0.32);
  static Color get inverseSurface => darkSurface;
  static Color get onInverseSurface => darkTextPrimary;
  static Color get inversePrimary => primaryLight;

  // ========== ألوان الطباعة والتقارير PDF ==========

  /// ألوان خاصة بالطباعة والتقارير لتحل محل PdfColors
  /// هذه الألوان متوافقة مع الطباعة وتعطي نتائج ممتازة

  /// خلفية رؤوس الجداول - رمادي فاتح للطباعة
  static const Color pdfHeaderBackground = Color(0xFFF5F5F5);

  /// لون النص في رؤوس الجداول - أسود قوي
  static const Color pdfHeaderText = Color(0xFF000000);

  /// لون النص العادي في التقارير - أسود متوسط
  static const Color pdfBodyText = Color(0xFF212121);

  /// لون النص الثانوي في التقارير - رمادي متوسط
  static const Color pdfSecondaryText = Color(0xFF757575);

  /// خلفية الصفوف المتناوبة في الجداول - رمادي فاتح جداً
  static const Color pdfAlternateRow = Color(0xFFFAFAFA);

  /// حدود الجداول والعناصر - رمادي متوسط
  static const Color pdfBorder = Color(0xFFE0E0E0);

  /// لون الخط الفاصل - رمادي فاتح
  static const Color pdfDivider = Color(0xFFEEEEEE);

  /// ألوان الحالات للطباعة
  static const Color pdfSuccess = Color(0xFF4CAF50); // أخضر للطباعة
  static const Color pdfWarning = Color(0xFFFF9800); // برتقالي للطباعة
  static const Color pdfError = Color(0xFFF44336); // أحمر للطباعة
  static const Color pdfInfo = Color(0xFF2196F3); // أزرق للطباعة

  // ========== ألوان حالات متقدمة جديدة ==========

  /// ألوان حالات الشبكة والاتصال
  static const Color networkConnected = Color(0xFF38A169); // متصل - أخضر
  static const Color networkDisconnected = Color(0xFFE53E3E); // منقطع - أحمر
  static const Color networkConnecting = Color(0xFFED8936); // يتصل - برتقالي
  static const Color networkUnknown = Color(0xFF718096); // غير معروف - رمادي

  /// ألوان حالات البطارية والطاقة
  static const Color batteryFull = Color(0xFF38A169); // ممتلئة - أخضر
  static const Color batteryMedium = Color(0xFFED8936); // متوسطة - برتقالي
  static const Color batteryLow = Color(0xFFE53E3E); // منخفضة - أحمر
  static const Color batteryCharging = Color(0xFF3182CE); // تشحن - أزرق

  /// ألوان حالات الأمان والحماية
  static const Color securityHigh = Color(0xFF38A169); // أمان عالي - أخضر
  static const Color securityMedium = Color(0xFFED8936); // أمان متوسط - برتقالي
  static const Color securityLow = Color(0xFFE53E3E); // أمان منخفض - أحمر
  static const Color securityUnknown = Color(0xFF718096); // غير معروف - رمادي

  /// ألوان حالات الأداء والسرعة
  static const Color performanceExcellent = Color(0xFF38A169); // ممتاز - أخضر
  static const Color performanceGood = Color(0xFF319795); // جيد - تركوازي
  static const Color performanceAverage = Color(0xFFED8936); // متوسط - برتقالي
  static const Color performancePoor = Color(0xFFE53E3E); // ضعيف - أحمر

  // ========== دوال مساعدة ذكية محسنة مع Material Design 3 ==========

  /// حساب التباين بين لونين وفقاً لمعايير WCAG 2.1
  static double calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final brightest = math.max(luminance1, luminance2);
    final darkest = math.min(luminance1, luminance2);
    return (brightest + 0.05) / (darkest + 0.05);
  }

  /// تحديد ما إذا كان اللون فاتحاً أم داكناً مع دقة محسنة
  static bool isLightColor(Color color) {
    return color.computeLuminance() > 0.5;
  }

  /// إنشاء لون أفتح من اللون المعطى مع الحفاظ على التشبع
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight =
        hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  /// إنشاء لون أغمق من اللون المعطى مع الحفاظ على التشبع
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  /// إنشاء لون بتشبع مختلف
  static Color adjustSaturation(Color color, double saturation) {
    assert(saturation >= 0 && saturation <= 1);
    final hsl = HSLColor.fromColor(color);
    return hsl.withSaturation(saturation).toColor();
  }

  /// إنشاء لون بدرجة لون مختلفة
  static Color adjustHue(Color color, double hue) {
    assert(hue >= 0 && hue <= 360);
    final hsl = HSLColor.fromColor(color);
    return hsl.withHue(hue).toColor();
  }
}

/// Extension لإضافة دوال مساعدة للألوان
extension ColorExtensions on Color {
  /// تغميق اللون بنسبة معينة
  Color darken(double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(this);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  /// تفتيح اللون بنسبة معينة
  Color lighten(double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(this);
    final hslLight =
        hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
}
