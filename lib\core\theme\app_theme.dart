import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/index.dart';

/// نظام الثيم الموحد لتطبيق تاجر بلس مع Material Design 3
/// يحتوي على جميع إعدادات التصميم للوضعين الفاتح والداكن مع ألوان عصرية ووصولية محسنة
/// 🎨 ألوان Material Design 3 العصرية
/// ♿ ضمان الوصولية WCAG 2.1
/// 🌈 تدرجات لونية متطورة
/// 🎭 تفاعلية محسنة
class AppTheme {
  // منع إنشاء كائن من هذا الكلاس
  AppTheme._();

  /// إنشاء ثيم ديناميكي بناءً على اللون المختار
  static ThemeData createLightTheme(String colorTheme) {
    final themeSettings = AppColors.availableThemes[colorTheme] ??
        AppColors.availableThemes['red']!; // Fallback to red
    final primaryColor = themeSettings['primary'] as Color;
    final primaryLight = themeSettings['primaryLight'] as Color;
    final secondaryColor =
        themeSettings['secondary'] as Color? ?? AppColors.secondary;
    final secondaryLightColor =
        themeSettings['secondaryLight'] as Color? ?? AppColors.secondaryLight;
    final accentColor = themeSettings['accent'] as Color? ?? AppColors.accent;
    final accentLightColor =
        themeSettings['accentLight'] as Color? ?? AppColors.accentLight;

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // نظام الألوان المحسن مع Material Design 3
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        primaryContainer: primaryLight,
        secondary: secondaryColor,
        secondaryContainer: secondaryLightColor,
        tertiary: accentColor,
        tertiaryContainer: accentLightColor,
        surface: AppColors.lightSurface,
        surfaceContainerHighest: AppColors.lightSurfaceVariant,
        error: AppColors.error,
        onPrimary: AppColors.getTextColorForBackground(primaryColor),
        onSecondary: AppColors.getTextColorForBackground(secondaryColor),
        onSurface: AppColors.lightTextPrimary,
        onError: AppColors.onError,
        outline: AppColors.lightBorder,
        shadow: AppColors.lightShadow,
        // ألوان إضافية للوصولية
        onPrimaryContainer: AppColors.lightTextPrimary,
        onSecondaryContainer: AppColors.lightTextPrimary,
        onTertiary: AppColors.getTextColorForBackground(accentColor),
        onTertiaryContainer: AppColors.lightTextPrimary,
        inversePrimary: primaryLight,
        inverseSurface: AppColors.darkSurface,
        onInverseSurface: AppColors.darkTextPrimary,
      ),

      // نظام الطباعة
      textTheme: AppTypography.lightTextTheme,

      // شريط التطبيق المحسن مع Material Design 3
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: AppColors.getTextColorForBackground(primaryColor),
        elevation: 0,
        centerTitle: true,
        titleTextStyle: AppTypography.lightTextTheme.headlineMedium?.copyWith(
          color: AppColors.getTextColorForBackground(primaryColor),
          fontWeight: AppTypography.weightSemiBold,
          letterSpacing: -0.25,
        ),
        iconTheme: IconThemeData(
          color: AppColors.getTextColorForBackground(primaryColor),
          size: AppDimensions.iconSizeMedium,
        ),
        actionsIconTheme: IconThemeData(
          color: AppColors.getTextColorForBackground(primaryColor),
          size: AppDimensions.iconSizeMedium,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 4,
      ),

      // البطاقات المحسنة مع Material Design 3
      cardTheme: CardTheme(
        color: AppColors.lightSurface,
        elevation: 2,
        shadowColor: AppColors.lightShadow,
        surfaceTintColor: primaryColor.withValues(alpha: 0.05),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        margin: AppDimensions.cardPaddingSmall,
        clipBehavior: Clip.antiAlias,
      ),

      // الأزرار المرفوعة المحسنة مع Material Design 3
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: AppColors.getTextColorForBackground(primaryColor),
          elevation: 3,
          shadowColor: primaryColor.withValues(alpha: 0.25),
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          padding: AppDimensions.buttonPaddingMedium,
          textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.weightSemiBold,
            letterSpacing: 0.1,
          ),
          minimumSize: const Size(120, 44),
        ),
      ),

      // الأزرار المحددة المحسنة مع Material Design 3
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          padding: AppDimensions.buttonPaddingMedium,
          textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
            color: primaryColor,
            fontWeight: AppTypography.weightSemiBold,
            letterSpacing: 0.1,
          ),
          minimumSize: const Size(120, 44),
        ),
      ),

      // الأزرار النصية المحسنة مع Material Design 3
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          ),
          padding: AppDimensions.buttonPaddingSmall,
          textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
            color: primaryColor,
            fontWeight: AppTypography.weightSemiBold,
            letterSpacing: 0.1,
          ),
          minimumSize: const Size(80, 36),
        ),
      ),

      // حقول الإدخال المحسنة مع Material Design 3
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.lightSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide:
              const BorderSide(color: AppColors.lightBorder, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide:
              const BorderSide(color: AppColors.lightBorder, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: primaryColor, width: 2.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: const BorderSide(color: AppColors.error, width: 2.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: const BorderSide(color: AppColors.error, width: 2.5),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        labelStyle: AppTypography.lightTextTheme.bodyMedium?.copyWith(
          color: primaryColor,
          fontWeight: AppTypography.weightMedium,
        ),
        hintStyle: AppTypography.lightTextTheme.bodyMedium?.copyWith(
          color: AppColors.lightTextHint,
          fontWeight: AppTypography.weightRegular,
        ),
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),

      // أشرطة التقدم
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: primaryColor,
        linearTrackColor: AppColors.lightSurfaceVariant,
        circularTrackColor: AppColors.lightSurfaceVariant,
      ),

      // المفاتيح
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.lightTextHint;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withValues(alpha: 0.3);
          }
          return AppColors.lightSurfaceVariant;
        }),
      ),

      // أزرار الراديو
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.lightTextHint;
        }),
      ),

      // مربعات الاختيار
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.lightSurface;
        }),
        checkColor: WidgetStateProperty.all(AppColors.onPrimary),
        side: const BorderSide(color: AppColors.lightBorder, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),

      // القوائم المنسدلة للوضع الفاتح
      dropdownMenuTheme: DropdownMenuThemeData(
        textStyle: AppTypography.lightTextTheme.bodyMedium,
        menuStyle: MenuStyle(
          backgroundColor: WidgetStateProperty.all(AppColors.lightSurface),
          surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
          elevation: WidgetStateProperty.all(4),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.lightSurfaceVariant,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.lightBorder),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.lightBorder),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: primaryColor, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),

      // الخلفية العامة
      scaffoldBackgroundColor: AppColors.lightBackground,

      // لون التركيز
      focusColor: primaryColor.withValues(alpha: 0.1),
      hoverColor: primaryColor.withValues(alpha: 0.05),
      highlightColor: primaryColor.withValues(alpha: 0.1),
      splashColor: primaryColor.withValues(alpha: 0.2),
    );
  }

  /// إنشاء ثيم داكن ديناميكي بناءً على اللون المختار
  static ThemeData createDarkTheme(String colorTheme) {
    final themeSettings = AppColors.availableThemes[colorTheme] ??
        AppColors.availableThemes['red']!; // Fallback to red
    final primaryColor = themeSettings['primary'] as Color;
    final primaryLight = themeSettings['primaryLight'] as Color;
    final secondaryColor =
        themeSettings['secondary'] as Color? ?? AppColors.secondary;
    final secondaryLightColor =
        themeSettings['secondaryLight'] as Color? ?? AppColors.secondaryLight;
    final accentColor = themeSettings['accent'] as Color? ?? AppColors.accent;
    final accentLightColor =
        themeSettings['accentLight'] as Color? ?? AppColors.accentLight;

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,

      // نظام الألوان للوضع الداكن المحسن مع حيوية وتباين عالي
      colorScheme: ColorScheme.dark(
        primary: primaryLight,
        primaryContainer: primaryColor,
        secondary: secondaryLightColor, // استخدام النسخة الفاتحة من الثانوي
        secondaryContainer: secondaryColor, // استخدام الثانوي الأساسي
        tertiary: accentLightColor, // استخدام النسخة الفاتحة من الأكسنت
        tertiaryContainer: accentColor, // استخدام الأكسنت الأساسي
        surface: AppColors.darkSurface,
        surfaceContainerHighest: AppColors.darkSurfaceVariant,
        surfaceContainer: AppColors.darkSurfaceElevated, // سطح مرفوع جديد
        error: AppColors.error,
        onPrimary: AppColors.darkBackground,
        onSecondary: AppColors.darkBackground,
        onSurface: AppColors.darkTextPrimary,
        onSurfaceVariant: AppColors.darkTextSecondary,
        onError: AppColors.onPrimary,
        outline: AppColors.darkBorder,
        outlineVariant: AppColors.darkDivider,
        shadow: AppColors.darkShadow,
        scrim: AppColors.darkOverlay, // تراكب جديد
      ),

      // نظام الطباعة للوضع الداكن
      textTheme: AppTypography.darkTextTheme,

      // شريط التطبيق للوضع الداكن
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: AppTypography.darkTextTheme.headlineMedium?.copyWith(
          color: AppColors.onPrimary,
          fontWeight: AppTypography.weightSemiBold,
        ),
        iconTheme: const IconThemeData(
          color: AppColors.onPrimary,
          size: 24,
        ),
        actionsIconTheme: const IconThemeData(
          color: AppColors.onPrimary,
          size: 24,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),

      // البطاقات للوضع الداكن المحسنة مع الألوان الجديدة
      cardTheme: CardTheme(
        color: AppColors.darkSurface,
        elevation: 4, // زيادة الارتفاع للمزيد من العمق
        shadowColor: AppColors.darkShadow,
        surfaceTintColor:
            primaryLight.withValues(alpha: 0.05), // لمسة لونية خفيفة
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16), // زوايا أكثر نعومة
          side: BorderSide(
            color: AppColors.darkBorder.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),

      // الأزرار المرفوعة للوضع الداكن
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryLight,
          foregroundColor: AppColors.darkBackground,
          elevation: 2,
          shadowColor: primaryLight.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: AppTypography.darkTextTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.weightMedium,
          ),
        ),
      ),

      // الأزرار المحددة للوضع الداكن
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryLight,
          side: BorderSide(color: primaryLight, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: AppTypography.darkTextTheme.labelLarge?.copyWith(
            color: primaryLight,
            fontWeight: AppTypography.weightMedium,
          ),
        ),
      ),

      // الأزرار النصية للوضع الداكن
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryLight,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: AppTypography.darkTextTheme.labelLarge?.copyWith(
            color: primaryLight,
            fontWeight: AppTypography.weightMedium,
          ),
        ),
      ),

      // حقول الإدخال للوضع الداكن المحسنة مع الألوان الجديدة
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor:
            AppColors.darkSurfaceElevated, // استخدام السطح المرفوع الجديد
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16), // زوايا أكثر نعومة
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(
            color: AppColors.darkBorder.withValues(alpha: 0.6),
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: primaryLight, width: 2.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        labelStyle: AppTypography.darkTextTheme.bodyMedium?.copyWith(
          color: AppColors.darkTextSecondary,
          fontWeight: FontWeight.w500,
        ),
        hintStyle: AppTypography.darkTextTheme.bodyMedium?.copyWith(
          color: AppColors.darkTextHint,
        ),
        floatingLabelStyle: TextStyle(
          color: primaryLight,
          fontWeight: FontWeight.w600,
        ),
      ),

      // أشرطة التقدم للوضع الداكن
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: primaryLight,
        linearTrackColor: AppColors.darkSurfaceVariant,
        circularTrackColor: AppColors.darkSurfaceVariant,
      ),

      // المفاتيح للوضع الداكن
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryLight;
          }
          return AppColors.darkTextHint;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryLight.withValues(alpha: 0.3);
          }
          return AppColors.darkSurfaceVariant;
        }),
      ),

      // أزرار الراديو للوضع الداكن
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryLight;
          }
          return AppColors.darkTextHint;
        }),
      ),

      // مربعات الاختيار للوضع الداكن
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryLight;
          }
          return AppColors.darkSurface;
        }),
        checkColor: WidgetStateProperty.all(AppColors.darkBackground),
        side: const BorderSide(color: AppColors.darkBorder, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),

      // القوائم المنسدلة للوضع الداكن
      dropdownMenuTheme: DropdownMenuThemeData(
        textStyle: AppTypography.darkTextTheme.bodyMedium,
        menuStyle: MenuStyle(
          backgroundColor: WidgetStateProperty.all(AppColors.darkSurface),
          surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
          elevation: WidgetStateProperty.all(4),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.darkSurfaceVariant,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.darkBorder),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.darkBorder),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: primaryLight, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),

      // الخلفية العامة للوضع الداكن
      scaffoldBackgroundColor: AppColors.darkBackground,

      // ألوان التفاعل للوضع الداكن
      focusColor: primaryLight.withValues(alpha: 0.1),
      hoverColor: primaryLight.withValues(alpha: 0.05),
      highlightColor: primaryLight.withValues(alpha: 0.1),
      splashColor: primaryLight.withValues(alpha: 0.2),
    );
  }

  // ========== الثيم الافتراضي المحدث ==========

  /// الثيم الفاتح الافتراضي - يستخدم الألوان العصرية الجديدة
  static ThemeData get lightTheme => createLightTheme('red');

  /// الثيم الداكن الافتراضي - يستخدم الألوان العصرية الجديدة
  static ThemeData get darkTheme => createDarkTheme('red');

  // ========== ثيمات إضافية جاهزة ==========

  /// ثيم أزرق مهني
  static ThemeData get blueTheme => createLightTheme('blue');
  static ThemeData get blueDarkTheme => createDarkTheme('blue');

  /// ثيم أخضر طبيعي
  static ThemeData get greenTheme => createLightTheme('green');
  static ThemeData get greenDarkTheme => createDarkTheme('green');

  /// ثيم بنفسجي أنيق
  static ThemeData get purpleTheme => createLightTheme('purple');
  static ThemeData get purpleDarkTheme => createDarkTheme('purple');
}
