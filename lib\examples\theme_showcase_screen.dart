import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/theme/index.dart';
import '../core/widgets/enhanced_ui_components.dart';

/// شاشة عرض التحسينات الجديدة على نظام الثيمات
/// 🎨 تعرض جميع الألوان والثيمات الجديدة بشكل تفاعلي
class ThemeShowcaseScreen extends StatefulWidget {
  const ThemeShowcaseScreen({Key? key}) : super(key: key);

  @override
  State<ThemeShowcaseScreen> createState() => _ThemeShowcaseScreenState();
}

class _ThemeShowcaseScreenState extends State<ThemeShowcaseScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('🎨 عرض تحسينات الثيمات'),
        centerTitle: true,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'الألوان'),
            Tab(icon: Icon(Icons.dashboard), text: 'البطاقات'),
            Tab(icon: Icon(Icons.color_lens), text: 'الثيمات'),
            Tab(icon: Icon(Icons.accessibility), text: 'الوصولية'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildColorsTab(),
          _buildCardsTab(),
          _buildThemesTab(),
          _buildAccessibilityTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          final themeManager =
              Provider.of<ThemeManager>(context, listen: false);
          themeManager.toggleTheme();
        },
        icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
        label: Text(isDark ? 'الوضع الفاتح' : 'الوضع الداكن'),
      ),
    );
  }

  /// تبويب عرض الألوان الجديدة
  Widget _buildColorsTab() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          _buildSectionHeader(
            '🌙 ألوان الوضع الداكن المحسنة',
            'ألوان أكثر حيوية ووضوحاً',
          ),

          if (isDark) ...[
            // خلفيات
            _buildColorGroup('الخلفيات', [
              _buildColorTile('خلفية التطبيق', AppColors.darkBackground),
              _buildColorTile('سطح البطاقات', AppColors.darkSurface),
              _buildColorTile('سطح مرفوع', AppColors.darkSurfaceElevated),
            ]),

            const SizedBox(height: 24),

            // نصوص
            _buildColorGroup('النصوص', [
              _buildColorTile('نص أساسي', AppColors.darkTextPrimary),
              _buildColorTile('نص ثانوي', AppColors.darkTextSecondary),
              _buildColorTile('نص توضيحي', AppColors.darkTextHint),
            ]),

            const SizedBox(height: 24),

            // حدود وظلال
            _buildColorGroup('الحدود والظلال', [
              _buildColorTile('حدود', AppColors.darkBorder),
              _buildColorTile('فواصل', AppColors.darkDivider),
              _buildColorTile('ظل', AppColors.darkShadow),
              _buildColorTile('تراكب', AppColors.darkOverlay),
            ]),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.info),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'قم بالتبديل إلى الوضع الداكن لرؤية التحسينات الجديدة',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: AppColors.info,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// تبويب عرض البطاقات المحسنة
  Widget _buildCardsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            '📊 بطاقات الإحصائيات المحسنة',
            'تصميم أكثر عصرية وجاذبية',
          ),

          // بطاقات الإحصائيات
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              EnhancedUIComponents.statsCard(
                title: 'المبيعات',
                value: '25,480 ر.س',
                subtitle: 'هذا الشهر',
                icon: Icons.trending_up,
                context: context,
                customColor: AppColors.moduleSales,
                showTrend: true,
                trendValue: 12.5,
                isPositiveTrend: true,
                onTap: () => _showMessage('تم النقر على المبيعات'),
              ),
              EnhancedUIComponents.statsCard(
                title: 'الطلبات',
                value: '156',
                subtitle: 'طلب جديد',
                icon: Icons.shopping_bag,
                context: context,
                customColor: AppColors.modulePurchases,
                showTrend: true,
                trendValue: -3.2,
                isPositiveTrend: false,
                onTap: () => _showMessage('تم النقر على الطلبات'),
              ),
              EnhancedUIComponents.statsCard(
                title: 'العملاء',
                value: '1,247',
                subtitle: 'عميل نشط',
                icon: Icons.people,
                context: context,
                customColor: AppColors.moduleUsers,
                showTrend: true,
                trendValue: 8.7,
                isPositiveTrend: true,
                onTap: () => _showMessage('تم النقر على العملاء'),
              ),
              EnhancedUIComponents.statsCard(
                title: 'الأرباح',
                value: '8,950 ر.س',
                subtitle: 'صافي الربح',
                icon: Icons.account_balance_wallet,
                context: context,
                customColor: AppColors.success,
                showTrend: true,
                trendValue: 15.3,
                isPositiveTrend: true,
                onTap: () => _showMessage('تم النقر على الأرباح'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تبويب عرض الثيمات الجديدة
  Widget _buildThemesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            '🌙 الثيمات الليلية الجديدة',
            'ثيمات خاصة للوضع الداكن',
          ),

          // الثيمات الليلية
          _buildThemeCard(
              'midnight', 'ليلي أنيق وراقي', 'بنفسجي ليلي هادئ ومريح للعين'),
          _buildThemeCard('neon', 'نيون حيوي ومشرق', 'ألوان نيون مشرقة وحيوية'),
          _buildThemeCard(
              'aurora', 'شفق قطبي ساحر', 'تدرج من البنفسجي إلى الوردي'),

          const SizedBox(height: 24),

          _buildSectionHeader(
            '🎨 الثيمات الكلاسيكية المحسنة',
            'ثيمات محسنة مع ألوان أكثر حيوية',
          ),

          // الثيمات الكلاسيكية
          _buildThemeCard(
              'red', 'أحمر تاجر بلس العصري', 'اللون الأساسي للتطبيق'),
          _buildThemeCard('blue', 'أزرق مهني هادئ', 'مناسب للاستخدام المهني'),
          _buildThemeCard('green', 'أخضر طبيعي منعش', 'لون طبيعي ومريح'),
        ],
      ),
    );
  }

  /// تبويب عرض الوصولية
  Widget _buildAccessibilityTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            '♿ الوصولية والتباين',
            'معايير WCAG 2.1 AA للوصولية',
          ),

          // معلومات التباين
          _buildContrastInfo(),

          const SizedBox(height: 24),

          // أمثلة على النصوص
          _buildTextExamples(),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionHeader(String title, String subtitle) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مجموعة ألوان
  Widget _buildColorGroup(String title, List<Widget> colors) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...colors,
      ],
    );
  }

  /// بناء عنصر لون
  Widget _buildColorTile(String name, Color color) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '#${color.toARGB32().toRadixString(16).toUpperCase().padLeft(8, '0')}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة ثيم
  Widget _buildThemeCard(String themeKey, String name, String description) {
    final themeData = AppColors.availableThemes[themeKey];
    if (themeData == null) return const SizedBox.shrink();

    final primaryColor = themeData['primary'] as Color;
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            final themeManager =
                Provider.of<ThemeManager>(context, listen: false);
            themeManager.setColorTheme(themeKey);
            _showMessage('تم تطبيق ثيم: $name');
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor.withValues(alpha: 0.1),
                  primaryColor.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: themeData['gradient'] as List<Color>,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: primaryColor,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات التباين
  Widget _buildContrastInfo() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (!isDark) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.info.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          'قم بالتبديل إلى الوضع الداكن لرؤية معلومات التباين',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: AppColors.info,
          ),
        ),
      );
    }

    // حساب التباين
    final backgroundLuminance = AppColors.darkBackground.computeLuminance();
    final textLuminance = AppColors.darkTextPrimary.computeLuminance();
    final contrastRatio = (textLuminance + 0.05) / (backgroundLuminance + 0.05);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.accessibility_new,
                color: AppColors.success,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'نسبة التباين: ${contrastRatio.toStringAsFixed(2)}:1',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'يتجاوز معايير WCAG 2.1 AA (7:1) ✅',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.success,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'مناسب للأشخاص ذوي الإعاقة البصرية',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أمثلة النصوص
  Widget _buildTextExamples() {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أمثلة على النصوص',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // نص أساسي
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'نص أساسي',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              Text(
                'هذا نص أساسي بتباين عالي وواضح',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // نص ثانوي
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'نص ثانوي',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              Text(
                'هذا نص ثانوي مع تباين مناسب للقراءة',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// عرض رسالة
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
